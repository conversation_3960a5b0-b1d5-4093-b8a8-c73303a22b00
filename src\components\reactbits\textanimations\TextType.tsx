import { ElementType, useRef, useMemo, createElement } from "react";
import { useCurrentFrame, interpolate, useVideoConfig } from "remotion";

// 预设光标类型
type CursorPreset = "underscore" | "pipe" | "block" | "dot" | "solid";

interface TextTypeProps {
  className?: string;
  showCursor?: boolean;
  hideCursorWhileTyping?: boolean;
  cursorCharacter?: string | React.ReactNode;
  cursorPreset?: CursorPreset; // 预设光标类型
  cursorBlinkDuration?: number; // 光标闪烁周期
  cursorBlinkFrequency?: number; // 光标闪烁频率（每秒闪烁次数）
  cursorSize?: number; // 光标大小倍数（默认1）
  cursorClassName?: string;
  showCursorDuringDeleting?: boolean; // 删除时是否显示光标
  text: string | string[];
  as?: ElementType;
  textColors?: string[];
  onSentenceComplete?: (sentence: string, index: number) => void;
  reverseMode?: boolean;
  // Remotion Frame 控制参数
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数）
  charDuration?: number; // 单个字符动画持续时间（帧数）
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
  // 指定范围帧内的打印速度控制
  speedRanges?: Array<{
    startFrame: number;
    endFrame: number;
    charDuration: number; // 在此范围内的字符打印速度
  }>;
  // 删除动画控制
  enableDeleting?: boolean; // 是否启用删除动画
  deletingSpeed?: number; // 删除速度（帧数）
  deletingStartFrame?: number; // 删除动画开始帧
  deletingEndFrame?: number; // 删除动画结束帧
  pauseDuration?: number; // 打字完成后暂停时间（帧数）
  debug?: boolean; // 调试模式，在控制台输出状态信息
}

const TextType = ({
  text,
  as: Component = "div",
  className = "",
  showCursor = true,
  hideCursorWhileTyping = false,
  cursorCharacter,
  cursorPreset = "pipe",
  cursorClassName = "",
  cursorBlinkDuration = 0.5,
  cursorBlinkFrequency = 2, // 每秒闪烁2次
  cursorSize = 1,
  showCursorDuringDeleting = true, // 删除时是否显示光标
  textColors = [],
  onSentenceComplete,
  reverseMode = false,
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  duration,
  charDuration = 3,
  autoAdjustSpeed = true,
  speedRanges = [],
  // 删除动画参数
  enableDeleting = false,
  deletingSpeed = 2,
  deletingStartFrame,
  deletingEndFrame,
  pauseDuration = 30,
  ...props
}: TextTypeProps & React.HTMLAttributes<HTMLElement>) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const currentTextIndex = 0; // 简化为只支持单个文本
  const cursorRef = useRef<HTMLSpanElement>(null);

  const textArray = useMemo(() => (Array.isArray(text) ? text : [text]), [text]);

  // 获取光标字符（优先使用自定义字符，否则使用预设）
  const getCursorCharacter = () => {
    if (cursorCharacter !== undefined) {
      return cursorCharacter;
    }

    const presetCursors = {
      underscore: "_",
      pipe: "|",
      block: "▎",
      dot: "●",
      solid: "█",
    };

    return presetCursors[cursorPreset];
  };

  const finalCursorCharacter = getCursorCharacter();

  // 计算动画参数
  const getFrameAnimationParams = () => {
    if (endFrame !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: charDuration,
      };
    }

    if (duration !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: startFrame + duration,
        actualDelay: autoAdjustSpeed ? Math.max(1, Math.floor(duration / textArray[currentTextIndex].length / 2)) : charDuration,
      };
    }

    // 默认计算：基于文本长度和字符持续时间
    const totalFrames = textArray[currentTextIndex].length * charDuration;
    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + totalFrames,
      actualDelay: charDuration,
    };
  };

  const { actualStartFrame, actualEndFrame, actualDelay } = getFrameAnimationParams();

  // 获取当前字符的打印速度（支持指定范围内的速度控制）
  const getCurrentCharDuration = (charIndex: number) => {
    const charFrame = actualStartFrame + (charIndex * actualDelay);

    // 检查是否在任何速度范围内
    for (const range of speedRanges) {
      if (charFrame >= range.startFrame && charFrame <= range.endFrame) {
        return range.charDuration;
      }
    }

    return actualDelay;
  };

  const getCurrentTextColor = () => {
    if (textColors.length === 0) return "#ffffff";
    return textColors[currentTextIndex % textColors.length];
  };

  // 光标闪烁动画（基于帧）
  const getCursorOpacity = () => {
    if (!showCursor) return 0;

    // 如果动画还未开始，不显示光标
    if (frame < startFrame) return 0;

    // 如果删除动画已完成，不显示光标
    if (enableDeleting && deletingRange && frame > deletingRange.endFrame) return 0;

    // 如果设置了 endFrame 且已超过（但不在删除阶段），不显示光标
    if (endFrame !== undefined && frame > endFrame &&
        !(enableDeleting && deletingRange && frame >= deletingRange.startFrame && frame <= deletingRange.endFrame)) {
      return 0;
    }

    // 如果在删除阶段且设置了不显示光标，返回0
    if (enableDeleting && deletingRange && !showCursorDuringDeleting &&
      frame >= deletingRange.startFrame && frame < deletingRange.endFrame) {
      return 0;
    }

    // 如果在删除阶段且设置了显示光标，继续正常闪烁逻辑
    // （这个注释是为了明确逻辑流程）

    // 如果频率为0，光标常亮不闪烁
    if (cursorBlinkFrequency === 0) {
      return 1;
    }

    // 使用频率参数计算闪烁周期
    const blinkCycleInFrames = fps / cursorBlinkFrequency; // 一个完整闪烁周期的帧数
    const halfCycle = blinkCycleInFrames / 2; // 半个周期（显示或隐藏）

    // 从 startFrame 开始计算闪烁周期
    const effectiveFrame = Math.max(0, frame - startFrame);
    const cyclePosition = effectiveFrame % blinkCycleInFrames;

    // 使用 interpolate 创建平滑的淡入淡出效果
    const opacity = interpolate(
      cyclePosition,
      [0, halfCycle * 0.1, halfCycle * 0.9, halfCycle, halfCycle * 1.1, halfCycle * 1.9, blinkCycleInFrames],
      [1, 1, 1, 0, 0, 0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );

    return opacity;
  };

  // 计算打字阶段的结束帧
  const getTypingEndFrame = () => {
    const currentText = textArray[currentTextIndex];
    let currentFrame = actualStartFrame;

    for (let i = 0; i < currentText.length; i++) {
      const charDuration = getCurrentCharDuration(i);
      currentFrame += charDuration;
    }

    return currentFrame;
  };

  const typingEndFrame = getTypingEndFrame();

  // 计算删除动画的帧范围
  const getDeletingFrameRange = () => {
    if (!enableDeleting) return null;

    const actualDeletingStartFrame = deletingStartFrame !== undefined
      ? deletingStartFrame
      : typingEndFrame + pauseDuration;

    const actualDeletingEndFrame = deletingEndFrame !== undefined
      ? deletingEndFrame
      : actualDeletingStartFrame + (textArray[currentTextIndex].length * deletingSpeed);

    return {
      startFrame: actualDeletingStartFrame,
      endFrame: actualDeletingEndFrame,
    };
  };

  const deletingRange = getDeletingFrameRange();

  // 计算当前显示的文本（基于帧的动画逻辑）
  const getDisplayedText = () => {
    const currentText = textArray[currentTextIndex];
    const processedText = reverseMode
      ? currentText.split("").reverse().join("")
      : currentText;

    // 检查是否在删除阶段
    if (enableDeleting && deletingRange && frame >= deletingRange.startFrame) {
      if (frame >= deletingRange.endFrame) {
        return ""; // 删除完成
      }

      // 计算删除进度
      const deletingProgress = (frame - deletingRange.startFrame) / (deletingRange.endFrame - deletingRange.startFrame);
      const deletedLength = Math.floor(deletingProgress * processedText.length);
      return processedText.slice(0, processedText.length - deletedLength);
    }

    // 打字阶段
    let displayedLength = 0;
    let currentFrame = actualStartFrame;

    // 遍历每个字符，计算应该显示到哪个字符
    for (let i = 0; i < processedText.length; i++) {
      const charDuration = getCurrentCharDuration(i);
      const charEndFrame = currentFrame + charDuration;

      if (frame >= charEndFrame) {
        displayedLength = i + 1;
        currentFrame = charEndFrame;
      } else {
        break;
      }
    }

    return processedText.slice(0, displayedLength);
  };

  const displayedText = getDisplayedText();
  const cursorOpacity = getCursorOpacity();

  // 检查是否应该隐藏光标
  const isTyping = displayedText.length < textArray[currentTextIndex].length &&
    (!enableDeleting || !deletingRange || frame < deletingRange.startFrame);
  const isDeleting = enableDeleting && deletingRange &&
    frame >= deletingRange.startFrame && frame < deletingRange.endFrame;

  // 修复光标隐藏逻辑：删除时根据 showCursorDuringDeleting 参数决定
  // 注意：删除时光标应该始终显示在文本末尾，除非明确设置不显示
  const shouldHideCursor = (hideCursorWhileTyping && isTyping) ||
    (isDeleting && !showCursorDuringDeleting);

  return createElement(
    Component,
    {
      className: `inline-block whitespace-pre-wrap tracking-tight ${className}`,
      ...props,
    },
    <span className="inline" style={{ color: getCurrentTextColor() }}>
      {displayedText}
    </span>,
    showCursor && (
      <span
        ref={cursorRef}
        className={`ml-1 inline-block ${shouldHideCursor ? "hidden" : ""} ${cursorClassName}`}
        style={{
          opacity: cursorOpacity,
          fontSize: `${cursorSize}em`,
          transform: `scale(${cursorSize})`,
          transformOrigin: "bottom left"
        }}
      >
        {finalCursorCharacter}
      </span>
    )
  );
};

export default TextType;
