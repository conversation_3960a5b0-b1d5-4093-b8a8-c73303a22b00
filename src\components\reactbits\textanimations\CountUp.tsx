import { useEffect, useRef } from "react";
import { useInView, useMotionValue, useSpring } from "motion/react";
import { useCurrentFrame, interpolate } from "remotion";

interface CountUpProps {
  to: number;
  from?: number;
  direction?: "up" | "down";
  delay?: number;
  duration?: number;
  className?: string;
  startWhen?: boolean;
  separator?: string;
  onStart?: () => void;
  onEnd?: () => void;
  // Remotion Frame 支持
  startFrame?: number;
  endFrame?: number;
  frameDuration?: number; // 总动画持续时间（帧数）
  useFrameBasedAnimation?: boolean; // 是否使用基于帧的动画
  ease?: "linear" | "easeIn" | "easeOut" | "easeInOut"; // 缓动函数
}

export default function CountUp({
  to,
  from = 0,
  direction = "up",
  delay = 0,
  duration = 2,
  className = "",
  startWhen = true,
  separator = "",
  onStart,
  onEnd,
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  frameDuration = 60,
  useFrameBasedAnimation = false,
  ease = "easeOut",
}: CountUpProps) {
  const frame = useCurrentFrame();
  const ref = useRef<HTMLSpanElement>(null);
  const motionValue = useMotionValue(direction === "down" ? to : from);

  const damping = 20 + 40 * (1 / duration);
  const stiffness = 100 * (1 / duration);

  const springValue = useSpring(motionValue, {
    damping,
    stiffness,
  });

  const isInView = useInView(ref, { once: true, margin: "0px" });

  // 计算基于帧的动画参数
  const getFrameAnimationParams = () => {
    // 如果提供了 endFrame，使用它
    if (endFrame !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
      };
    }

    // 否则使用 frameDuration
    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + frameDuration,
    };
  };

  const { actualStartFrame, actualEndFrame } = getFrameAnimationParams();

  // 基于帧的当前值计算
  const getFrameBasedValue = () => {
    if (!useFrameBasedAnimation) return null;

    const easeFunction = {
      linear: (t: number) => t,
      easeIn: (t: number) => t * t,
      easeOut: (t: number) => 1 - Math.pow(1 - t, 2),
      easeInOut: (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2,
    }[ease];

    const progress = interpolate(
      frame,
      [actualStartFrame, actualEndFrame],
      [0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );

    const easedProgress = easeFunction(progress);

    if (direction === "down") {
      return to + (from - to) * easedProgress;
    } else {
      return from + (to - from) * easedProgress;
    }
  };

  // Get number of decimal places in a number
  const getDecimalPlaces = (num: number): number => {
    const str = num.toString();
    if (str.includes(".")) {
      const decimals = str.split(".")[1];
      if (parseInt(decimals) !== 0) {
        return decimals.length;
      }
    }
    return 0;
  };

  const maxDecimals = Math.max(getDecimalPlaces(from), getDecimalPlaces(to));

  // 基于帧的动画更新
  useEffect(() => {
    if (useFrameBasedAnimation && ref.current) {
      const frameValue = getFrameBasedValue();
      if (frameValue !== null) {
        const hasDecimals = maxDecimals > 0;

        const options: Intl.NumberFormatOptions = {
          useGrouping: !!separator,
          minimumFractionDigits: hasDecimals ? maxDecimals : 0,
          maximumFractionDigits: hasDecimals ? maxDecimals : 0,
        };

        const formattedNumber = Intl.NumberFormat("en-US", options).format(
          frameValue
        );

        ref.current.textContent = separator
          ? formattedNumber.replace(/,/g, separator)
          : formattedNumber;

        // 触发回调
        if (frame === actualStartFrame && onStart) {
          onStart();
        }
        if (frame >= actualEndFrame && onEnd) {
          onEnd();
        }
      }
    }
  }, [frame, useFrameBasedAnimation, getFrameBasedValue, maxDecimals, separator, actualStartFrame, actualEndFrame, onStart, onEnd]);

  useEffect(() => {
    if (!useFrameBasedAnimation && ref.current) {
      ref.current.textContent = String(direction === "down" ? to : from);
    }
  }, [from, to, direction, useFrameBasedAnimation]);

  useEffect(() => {
    if (!useFrameBasedAnimation && isInView && startWhen) {
      if (typeof onStart === "function") {
        onStart();
      }

      const timeoutId = setTimeout(() => {
        motionValue.set(direction === "down" ? from : to);
      }, delay * 1000);

      const durationTimeoutId = setTimeout(
        () => {
          if (typeof onEnd === "function") {
            onEnd();
          }
        },
        delay * 1000 + duration * 1000
      );

      return () => {
        clearTimeout(timeoutId);
        clearTimeout(durationTimeoutId);
      };
    }
  }, [
    useFrameBasedAnimation,
    isInView,
    startWhen,
    motionValue,
    direction,
    from,
    to,
    delay,
    onStart,
    onEnd,
    duration,
  ]);

  useEffect(() => {
    if (useFrameBasedAnimation) return;

    const unsubscribe = springValue.on("change", (latest) => {
      if (ref.current) {
        const hasDecimals = maxDecimals > 0;

        const options: Intl.NumberFormatOptions = {
          useGrouping: !!separator,
          minimumFractionDigits: hasDecimals ? maxDecimals : 0,
          maximumFractionDigits: hasDecimals ? maxDecimals : 0,
        };

        const formattedNumber = Intl.NumberFormat("en-US", options).format(
          latest
        );

        ref.current.textContent = separator
          ? formattedNumber.replace(/,/g, separator)
          : formattedNumber;
      }
    });

    return () => unsubscribe();
  }, [useFrameBasedAnimation, springValue, separator, maxDecimals]);

  return <span className={className} ref={ref} />;
}
