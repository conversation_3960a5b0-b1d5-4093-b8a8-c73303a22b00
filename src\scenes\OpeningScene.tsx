import React from 'react';
import { AbsoluteFill } from 'remotion';
import { SceneProps } from '../types';
import Subtitle from '../components/Subtitle';
import GradientText from '../components/reactbits/textanimations/GradientText';

const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {

  return (
    <AbsoluteFill className="text-white relative justify-center">
      {/* 主标题 - 全屏显示 */}
      <GradientText
        children="睡觉真的有用吗？"
        className="text-9xl font-bold text-center"
        startFrame={0}
        endFrame={60}
      />

      {/* 字幕区域 - 底部显示 */}
      <div className="absolute bottom-16 left-0 right-0 z-10 flex justify-center px-8">
        <Subtitle
          text="很多人相信：只要早睡，就能变健康、变聪明"
          startFrame={60}
          duration={120}
        />
      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;