import { ReactNode } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';

interface GradientTextProps {
    children: ReactNode;
    className?: string;
    colors?: string[];
    animationSpeed?: number;
    showBorder?: boolean;
    // Remotion Frame 控制参数
    startFrame?: number; // 动画开始帧
    endFrame?: number; // 动画结束帧
    duration?: number; // 动画持续时间（帧数）
    // 渐变动画控制参数
    gradientPattern?: number[] | number[][];
    // 渐变变化数组格式: [startFrame, endFrame, startPosition, endPosition] 或多个数组
    // startPosition/endPosition: 渐变位置百分比 (0-100)
    cycleFrames?: number; // 渐变循环周期（帧数，默认基于animationSpeed计算）
}

export default function GradientText({
    children,
    className = "",
    colors = ["#ffaa40", "#9c40ff", "#ffaa40"],
    animationSpeed = 8,
    showBorder = false,
    // Remotion Frame 参数
    startFrame = 0,
    endFrame,
    duration = 120, // 默认120帧
    // 渐变动画控制参数
    gradientPattern,
    cycleFrames,
}: GradientTextProps) {
    const frame = useCurrentFrame();

    // 计算实际的动画帧范围
    const actualStartFrame = startFrame;
    const actualEndFrame = endFrame || (startFrame + duration);

    // 检查是否在显示范围内
    const isVisible = frame >= actualStartFrame && frame <= actualEndFrame;

    // 计算渐变位置
    const getGradientPosition = () => {
        // 处理渐变模式
        const processPattern = (pattern: number[] | number[][]) => {
            // 检查是否是多个数组
            if (Array.isArray(pattern[0])) {
                // 多个数组格式
                const patterns = pattern as number[][];
                for (const singlePattern of patterns) {
                    const result = processSinglePattern(singlePattern);
                    if (result !== null) return result;
                }
            } else {
                // 单个数组格式
                const result = processSinglePattern(pattern as number[]);
                if (result !== null) return result;
            }
            return null;
        };

        const processSinglePattern = (pattern: number[]) => {
            if (pattern.length >= 4) {
                const [patternStart, patternEnd, startPos, endPos] = pattern;
                if (frame >= patternStart && frame <= patternEnd) {
                    const progress = (frame - patternStart) / (patternEnd - patternStart);
                    return interpolate(progress, [0, 1], [startPos, endPos], {
                        extrapolateLeft: 'clamp',
                        extrapolateRight: 'clamp',
                    });
                }
            }
            return null;
        };

        // 检查是否有自定义渐变模式
        if (gradientPattern) {
            const patternResult = processPattern(gradientPattern);
            if (patternResult !== null) return patternResult;
        }

        // 默认循环动画
        const defaultCycleFrames = cycleFrames || (animationSpeed * 30); // 假设30fps
        const cycleProgress = (frame % defaultCycleFrames) / defaultCycleFrames;

        // 创建往返动画 (0 -> 100 -> 0)
        const position = cycleProgress <= 0.5
            ? interpolate(cycleProgress, [0, 0.5], [0, 100])
            : interpolate(cycleProgress, [0.5, 1], [100, 0]);

        return position;
    };

    const gradientPosition = getGradientPosition();

    const gradientStyle = {
        backgroundImage: `linear-gradient(to right, ${colors.join(", ")})`,
        backgroundPosition: `${gradientPosition}% 50%`,
        backgroundSize: "300% 100%",
    };

    // 如果不在显示时间范围内，不渲染
    if (!isVisible) {
        return null;
    }

    return (
        <div
            className={`relative mx-auto flex max-w-fit flex-row items-center justify-center rounded-[1.25rem] font-medium backdrop-blur transition-shadow duration-500 overflow-hidden cursor-pointer ${className}`}
        >
            {showBorder && (
                <div
                    className="absolute inset-0 bg-cover z-0 pointer-events-none"
                    style={gradientStyle}
                >
                    <div
                        className="absolute inset-0 bg-black rounded-[1.25rem] z-[-1]"
                        style={{
                            width: "calc(100% - 2px)",
                            height: "calc(100% - 2px)",
                            left: "50%",
                            top: "50%",
                            transform: "translate(-50%, -50%)",
                        }}
                    ></div>
                </div>
            )}
            <div
                className="inline-block relative z-2 text-transparent bg-cover"
                style={{
                    ...gradientStyle,
                    backgroundClip: "text",
                    WebkitBackgroundClip: "text",
                }}
            >
                {children}
            </div>
        </div>
    );
}