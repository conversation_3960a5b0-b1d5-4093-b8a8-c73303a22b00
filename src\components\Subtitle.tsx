import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';

interface SubtitleProps {
  text: string;
  startFrame: number;
  duration: number;
  className?: string;
}

const Subtitle: React.FC<SubtitleProps> = ({
  text,
  startFrame,
  duration,
  className = '',
}) => {
  const frame = useCurrentFrame();
  
  // 淡入淡出动画
  const fadeInDuration = 20; // 淡入时长（帧）
  const fadeOutDuration = 20; // 淡出时长（帧）
  const endFrame = startFrame + duration;
  
  // 计算透明度
  const opacity = interpolate(
    frame,
    [
      startFrame,
      startFrame + fadeInDuration,
      endFrame - fadeOutDuration,
      endFrame,
    ],
    [0, 1, 1, 0],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  // 如果不在显示时间范围内，不渲染
  if (frame < startFrame || frame > endFrame) {
    return null;
  }

  return (
    <div
      className={`
        inline-block
        px-6 py-3
        bg-gray-200 bg-opacity-90
        rounded-xl
        backdrop-blur-sm
        shadow-lg
        ${className}
      `}
      style={{ opacity }}
    >
      <p className="text-gray-800 text-3xl font-medium text-center leading-relaxed">
        {text}
      </p>
    </div>
  );
};

export default Subtitle;
