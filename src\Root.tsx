import "./index.css";
import { Composition } from "remotion";
import { MyComposition } from "./Composition";

export const RemotionRoot: React.FC = () => {
  const fps = 30;
  const totalDurationInFrames = 78 * fps;

  return (
    <>
      <Composition
        id="EarlySleepMyth"
        component={MyComposition}
        durationInFrames={totalDurationInFrames}
        fps={fps}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
    </>
  );
};
