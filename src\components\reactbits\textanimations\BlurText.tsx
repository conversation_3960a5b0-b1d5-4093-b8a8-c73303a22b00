import { useMemo } from "react";
import { useCurrentFrame, interpolate } from "remotion";

type BlurTextProps = {
  text?: string;
  delay?: number;
  className?: string;
  animateBy?: "words" | "letters";
  direction?: "top" | "bottom";
  animationFrom?: Record<string, string | number>;
  animationTo?: Array<Record<string, string | number>>;
  onAnimationComplete?: () => void;
  stepDuration?: number;
  // Remotion Frame 控制参数
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数）
  elementDuration?: number; // 单个元素动画持续时间（帧数）
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
  ease?: "linear" | "easeIn" | "easeOut" | "easeInOut"; // 缓动函数
};



const BlurText: React.FC<BlurTextProps> = ({
  text = "",
  delay = 3, // 改为帧数延迟，默认3帧
  className = "",
  animateBy = "words",
  direction = "top",
  animationFrom,
  animationTo,
  onAnimationComplete,
  stepDuration = 20, // 改为帧数，默认20帧
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  duration,
  elementDuration = 30,
  autoAdjustSpeed = true,
  ease = "easeOut",
}) => {
  const frame = useCurrentFrame();
  const elements = animateBy === "words" ? text.split(" ") : text.split("");

  // 计算动画参数
  const getFrameAnimationParams = () => {
    if (endFrame !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: delay,
      };
    }

    if (duration !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: startFrame + duration,
        actualDelay: autoAdjustSpeed ? Math.max(1, Math.floor(duration / elements.length / 3)) : delay,
      };
    }

    // 默认计算：基于元素数量和延迟
    const totalFrames = elements.length * delay + elementDuration;
    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + totalFrames,
      actualDelay: delay,
    };
  };

  const { actualStartFrame, actualDelay } = getFrameAnimationParams();

  // 检查动画是否完成并触发回调
  const lastElementStartFrame = actualStartFrame + ((elements.length - 1) * actualDelay);
  const lastElementEndFrame = lastElementStartFrame + stepDuration;
  const isAnimationComplete = frame >= lastElementEndFrame;

  // 触发动画完成回调（只触发一次）
  if (isAnimationComplete && onAnimationComplete && frame === lastElementEndFrame) {
    onAnimationComplete();
  }

  const defaultFrom = useMemo(
    () =>
      direction === "top"
        ? { filter: "blur(10px)", opacity: 0, y: -50 }
        : { filter: "blur(10px)", opacity: 0, y: 50 },
    [direction]
  );

  const defaultTo = useMemo(
    () => [
      {
        filter: "blur(5px)",
        opacity: 0.5,
        y: direction === "top" ? 5 : -5,
      },
      { filter: "blur(0px)", opacity: 1, y: 0 },
    ],
    [direction]
  );

  const fromSnapshot = animationFrom ?? defaultFrom;
  const toSnapshots = animationTo ?? defaultTo;

  // 缓动函数
  const easeFunction = {
    linear: (t: number) => t,
    easeIn: (t: number) => t * t,
    easeOut: (t: number) => 1 - Math.pow(1 - t, 2),
    easeInOut: (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2,
  }[ease];

  // 计算每个元素的动画样式
  const getElementStyle = (index: number) => {
    const elementStartFrame = actualStartFrame + (index * actualDelay);
    const elementEndFrame = elementStartFrame + stepDuration;

    // 计算当前元素的进度
    const progress = interpolate(
      frame,
      [elementStartFrame, elementEndFrame],
      [0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );

    const easedProgress = easeFunction(progress);

    // 在多个关键帧之间插值
    if (toSnapshots.length === 1) {
      // 只有一个目标状态
      const target = toSnapshots[0];
      return {
        filter: interpolateValue(fromSnapshot.filter, target.filter, easedProgress),
        opacity: interpolateValue(fromSnapshot.opacity, target.opacity, easedProgress),
        transform: `translateY(${interpolateValue(fromSnapshot.y, target.y, easedProgress)}px)`,
      };
    } else {
      // 多个关键帧
      const stepProgress = easedProgress * toSnapshots.length;
      const currentStepIndex = Math.floor(stepProgress);
      const stepLocalProgress = stepProgress - currentStepIndex;

      if (currentStepIndex >= toSnapshots.length) {
        // 动画完成，使用最后一个状态
        const finalTarget = toSnapshots[toSnapshots.length - 1];
        return {
          filter: finalTarget.filter as string,
          opacity: finalTarget.opacity as number,
          transform: `translateY(${finalTarget.y}px)`,
        };
      }

      const currentTarget = currentStepIndex === 0 ? fromSnapshot : toSnapshots[currentStepIndex - 1];
      const nextTarget = toSnapshots[currentStepIndex];

      return {
        filter: interpolateValue(currentTarget.filter, nextTarget.filter, stepLocalProgress),
        opacity: interpolateValue(currentTarget.opacity, nextTarget.opacity, stepLocalProgress),
        transform: `translateY(${interpolateValue(currentTarget.y, nextTarget.y, stepLocalProgress)}px)`,
      };
    }
  };

  // 辅助函数：插值计算
  const interpolateValue = (from: any, to: any, progress: number): any => {
    if (typeof from === "number" && typeof to === "number") {
      return from + (to - from) * progress;
    }
    if (typeof from === "string" && typeof to === "string") {
      // 处理 blur 滤镜
      if (from.includes("blur") && to.includes("blur")) {
        const fromValue = parseFloat(from.match(/blur\(([^)]+)\)/)?.[1] || "0");
        const toValue = parseFloat(to.match(/blur\(([^)]+)\)/)?.[1] || "0");
        const interpolatedValue = fromValue + (toValue - fromValue) * progress;
        return `blur(${interpolatedValue}px)`;
      }
    }
    return progress < 0.5 ? from : to;
  };

  return (
    <p className={`blur-text ${className} flex flex-wrap`}>
      {elements.map((segment, index) => {
        const style = getElementStyle(index);

        return (
          <span
            key={index}
            style={{
              display: "inline-block",
              willChange: "transform, filter, opacity",
              ...style,
            }}
          >
            {segment === " " ? "\u00A0" : segment}
            {animateBy === "words" && index < elements.length - 1 && "\u00A0"}
          </span>
        );
      })}
    </p>
  );
};

export default BlurText;
