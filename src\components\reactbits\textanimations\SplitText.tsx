import React, { useRef, useEffect, useState } from "react";
import { useCurrentFrame, interpolate } from "remotion";
import { gsap } from "gsap";
import { SplitText as GSAPSplitText } from "gsap/SplitText";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(GSAPSplitText, useGSAP);

export interface SplitTextProps {
  text: string;
  className?: string;
  delay?: number; // 毫秒延迟，会转换为帧
  startFrame?: number;
  endFrame?: number;
  duration?: number; // 总动画持续时间（帧数），会自动计算 delay
  charDuration?: number; // 单个字符动画持续时间（帧数）
  autoAdjustSpeed?: boolean; // 是否根据持续时间自动调整速度
  ease?: string | ((t: number) => number); // GSAP 缓动函数
  splitType?: "chars" | "words" | "lines" | "words, chars";
  from?: gsap.TweenVars; // 使用 GSAP 的 TweenVars 类型
  to?: gsap.TweenVars;   // 使用 GSAP 的 TweenVars 类型
  tag?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span";
  textAlign?: React.CSSProperties["textAlign"];
  onAnimationComplete?: () => void;
}

const SplitText: React.FC<SplitTextProps> = ({
  text,
  className = "",
  delay,
  startFrame = 0,
  endFrame,
  duration,
  charDuration = 20,
  autoAdjustSpeed = true,
  ease = "elastic.out(1, 0.4)",
  splitType = "chars",
  from = { opacity: 0, y: 40 },
  to = { opacity: 1, y: 0 },
  tag = "p",
  textAlign = "center",
  onAnimationComplete,
}) => {
  const frame = useCurrentFrame();
  const ref = useRef<HTMLParagraphElement>(null);
  const [splitInstance, setSplitInstance] = useState<GSAPSplitText | null>(null);
  const [targets, setTargets] = useState<Element[]>([]);
  const [fontsLoaded, setFontsLoaded] = useState<boolean>(false);

  // 计算实际的动画参数
  const getAnimationParams = () => {
    const textLength = text.length;

    // 如果提供了 duration，自动计算其他参数
    if (duration && autoAdjustSpeed) {
      const calculatedEndFrame = startFrame + duration;
      const calculatedDelay = Math.max(1, Math.floor(duration / (textLength * 1.5))); // 确保有足够的重叠

      return {
        actualStartFrame: startFrame,
        actualEndFrame: calculatedEndFrame,
        actualDelay: calculatedDelay,
      };
    }

    // 如果提供了 endFrame 和 delay，直接使用
    if (endFrame !== undefined && delay !== undefined) {
      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: delay,
      };
    }

    // 如果只提供了 endFrame，自动计算 delay
    if (endFrame !== undefined) {
      const totalDuration = endFrame - startFrame;
      const calculatedDelay = Math.max(1, Math.floor(totalDuration / (textLength * 1.5)));

      return {
        actualStartFrame: startFrame,
        actualEndFrame: endFrame,
        actualDelay: calculatedDelay,
      };
    }

    // 默认值
    const defaultDuration = 60;
    const defaultDelay = Math.max(1, Math.floor(defaultDuration / (textLength * 1.5)));

    return {
      actualStartFrame: startFrame,
      actualEndFrame: startFrame + defaultDuration,
      actualDelay: defaultDelay,
    };
  };

  const { actualStartFrame, actualEndFrame, actualDelay } = getAnimationParams();

  // Initialize fonts loading
  useEffect(() => {
    if (document.fonts.status === "loaded") {
      setFontsLoaded(true);
    } else {
      document.fonts.ready.then(() => {
        setFontsLoaded(true);
      });
    }
  }, []);

  // Initialize SplitText when fonts are loaded
  useEffect(() => {
    if (!ref.current || !text || !fontsLoaded) return;

    const el = ref.current;
    const split = new GSAPSplitText(el, {
      type: splitType,
      smartWrap: true,
      autoSplit: splitType === "lines",
      linesClass: "split-line",
      wordsClass: "split-word",
      charsClass: "split-char",
      reduceWhiteSpace: false,
    });

    setSplitInstance(split);

    // Determine targets based on split type
    let newTargets: Element[] = [];
    if (splitType.includes("chars") && split.chars?.length) {
      newTargets = split.chars;
    } else if (splitType.includes("words") && split.words?.length) {
      newTargets = split.words;
    } else if (splitType.includes("lines") && split.lines?.length) {
      newTargets = split.lines;
    }

    setTargets(newTargets);

    return () => {
      try {
        split.revert();
      } catch (_) {}
    };
  }, [text, splitType, fontsLoaded]);

  // 使用 GSAP 进行动画，但基于 Remotion 的 frame 控制
  useGSAP(() => {
    if (!targets.length) return;

    targets.forEach((target, index) => {
      const elementStartFrame = actualStartFrame + (index * actualDelay);
      const elementEndFrame = elementStartFrame + charDuration;

      // 计算当前元素的进度
      const progress = interpolate(
        frame,
        [elementStartFrame, elementEndFrame],
        [0, 1],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );

      // 使用 GSAP 的 set 方法直接设置样式，利用 GSAP 的缓动
      gsap.set(target, {
        ...from,
        ...Object.keys(to).reduce((acc, key) => {
          const fromValue = (from as any)[key] ?? (key === 'opacity' ? 0 : key === 'scale' ? 1 : 0);
          const toValue = (to as any)[key] ?? (key === 'opacity' ? 1 : key === 'scale' ? 1 : 0);

          // 使用 GSAP 的缓动函数计算值
          const easedProgress = gsap.parseEase(ease)(progress);
          acc[key] = fromValue + (toValue - fromValue) * easedProgress;
          return acc;
        }, {} as any),
        force3D: true,
        willChange: "transform, opacity"
      });
    });

    // 检查动画是否完成
    const lastElementEndFrame = actualStartFrame + ((targets.length - 1) * actualDelay) + charDuration;
    if (frame >= lastElementEndFrame && onAnimationComplete) {
      onAnimationComplete();
    }
  }, {
    dependencies: [frame, targets, actualStartFrame, actualDelay, charDuration, ease, from, to, onAnimationComplete],
    scope: ref
  });

  const renderTag = () => {
    const style: React.CSSProperties = {
      textAlign,
      wordWrap: "break-word",
      willChange: "transform, opacity",
    };
    const classes = `split-parent overflow-hidden inline-block whitespace-normal ${className}`;
    switch (tag) {
      case "h1":
        return (
          <h1 ref={ref} style={style} className={classes}>
            {text}
          </h1>
        );
      case "h2":
        return (
          <h2 ref={ref} style={style} className={classes}>
            {text}
          </h2>
        );
      case "h3":
        return (
          <h3 ref={ref} style={style} className={classes}>
            {text}
          </h3>
        );
      case "h4":
        return (
          <h4 ref={ref} style={style} className={classes}>
            {text}
          </h4>
        );
      case "h5":
        return (
          <h5 ref={ref} style={style} className={classes}>
            {text}
          </h5>
        );
      case "h6":
        return (
          <h6 ref={ref} style={style} className={classes}>
            {text}
          </h6>
        );
      default:
        return (
          <p ref={ref} style={style} className={classes}>
            {text}
          </p>
        );
    }
  };

  return renderTag();
};

export default SplitText;
