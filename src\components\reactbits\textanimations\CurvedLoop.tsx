import {
    useRef,
    useEffect,
    useState,
    useMemo,
    useId,
    FC,
  } from "react";
  import { useCurrentFrame, interpolate } from 'remotion';

  interface CurvedLoopProps {
    marqueeText?: string;
    speed?: number;
    className?: string;
    curveAmount?: number;
    direction?: "left" | "right";
    // Remotion Frame 控制参数
    startFrame?: number; // 动画开始帧
    endFrame?: number; // 动画结束帧
    duration?: number; // 动画持续时间（帧数）
    // 模拟鼠标悬停和滑动参数
    hoverPattern?: number[]; // 悬停数组格式: [startFrame, endFrame, speedMultiplier?]
    // startFrame/endFrame: 悬停的帧范围
    // speedMultiplier: 悬停时的速度倍数（可选，默认为2）
    dragPattern?: number[]; // 拖拽数组格式: [startFrame, endFrame, dragDistance, dragSpeed?]
    // startFrame/endFrame: 拖拽的帧范围
    // dragDistance: 拖拽距离（正数向右，负数向左）
    // dragSpeed: 拖拽速度（可选，默认为1）
  }
  
  const CurvedLoop: FC<CurvedLoopProps> = ({
    marqueeText = "",
    speed = 2,
    className,
    curveAmount = 400,
    direction = "left",
    // Remotion Frame 参数
    startFrame = 0,
    endFrame,
    duration = 120, // 默认120帧
    // 模拟鼠标悬停和拖拽参数
    hoverPattern, // [startFrame, endFrame, speedMultiplier?]
    dragPattern, // [startFrame, endFrame, dragDistance, dragSpeed?]
  }) => {
    const frame = useCurrentFrame();
    const text = useMemo(() => {
      const hasTrailing = /\s|\u00A0$/.test(marqueeText);
      return (
        (hasTrailing ? marqueeText.replace(/\s+$/, "") : marqueeText) + "\u00A0"
      );
    }, [marqueeText]);
  
    const measureRef = useRef<SVGTextElement | null>(null);
    const textPathRef = useRef<SVGTextPathElement | null>(null);
    const pathRef = useRef<SVGPathElement | null>(null);
    const [spacing, setSpacing] = useState(0);
    const [offset, setOffset] = useState(0);
    const uid = useId();
    const pathId = `curve-${uid}`;
    const pathD = `M-100,40 Q500,${40 + curveAmount} 1540,40`;
  
    // 计算实际的动画帧范围
    const actualStartFrame = startFrame;
    const actualEndFrame = endFrame || (startFrame + duration);

    // 检查是否在显示范围内
    const isVisible = frame >= actualStartFrame && frame <= actualEndFrame;

    const textLength = spacing;
    const totalText = textLength
      ? Array(Math.ceil(1800 / textLength) + 2)
          .fill(text)
          .join("")
      : text;
    const ready = spacing > 0;
  
    useEffect(() => {
      if (measureRef.current)
        setSpacing(measureRef.current.getComputedTextLength());
    }, [text, className]);
  
    useEffect(() => {
      if (!spacing) return;
      if (textPathRef.current) {
        const initial = -spacing;
        textPathRef.current.setAttribute("startOffset", initial + "px");
        setOffset(initial);
      }
    }, [spacing]);
  
    // 基于帧计算当前速度和方向
    const getCurrentSpeed = () => {
      let currentSpeed = speed;
      let currentDirection = direction;

      // 检查是否在悬停状态
      if (hoverPattern && hoverPattern.length >= 2) {
        const [hoverStart, hoverEnd, speedMultiplier = 2] = hoverPattern;
        if (frame >= hoverStart && frame <= hoverEnd) {
          currentSpeed *= speedMultiplier;
        }
      }

      // 检查是否在拖拽状态
      if (dragPattern && dragPattern.length >= 3) {
        const [dragStart, dragEnd, dragDistance, dragSpeed = 1] = dragPattern;
        if (frame >= dragStart && frame <= dragEnd) {
          // 计算拖拽进度
          const dragProgress = (frame - dragStart) / (dragEnd - dragStart);
          const dragOffset = interpolate(dragProgress, [0, 1], [0, dragDistance], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          });

          // 根据拖拽距离确定方向
          currentDirection = dragDistance > 0 ? "right" : "left";
          currentSpeed = Math.abs(dragOffset) * dragSpeed;
        }
      }

      return { speed: currentSpeed, direction: currentDirection };
    };

    useEffect(() => {
      if (!spacing || !ready || !isVisible) return;

      if (textPathRef.current) {
        const { speed: currentSpeed, direction: currentDirection } = getCurrentSpeed();
        const delta = currentDirection === "right" ? currentSpeed : -currentSpeed;
        const currentOffset = parseFloat(
          textPathRef.current.getAttribute("startOffset") || "0"
        );
        let newOffset = currentOffset + delta;
        const wrapPoint = spacing;
        if (newOffset <= -wrapPoint) newOffset += wrapPoint;
        if (newOffset > 0) newOffset -= wrapPoint;
        textPathRef.current.setAttribute("startOffset", newOffset + "px");
        setOffset(newOffset);
      }
    }, [spacing, speed, ready, frame, isVisible, hoverPattern, dragPattern]);
  
    // 如果不在显示时间范围内，不渲染
    if (!isVisible) {
      return null;
    }
  
    return (
      <div
        className="min-h-screen flex items-center justify-center w-full"
        style={{ visibility: ready ? "visible" : "hidden" }}
      >
        <svg
          className="select-none w-full overflow-visible block aspect-[100/12] text-[6rem] font-bold uppercase leading-none"
          viewBox="0 0 1440 120"
        >
          <text
            ref={measureRef}
            xmlSpace="preserve"
            style={{ visibility: "hidden", opacity: 0, pointerEvents: "none" }}
          >
            {text}
          </text>
          <defs>
            <path
              ref={pathRef}
              id={pathId}
              d={pathD}
              fill="none"
              stroke="transparent"
            />
          </defs>
          {ready && (
            <text xmlSpace="preserve" className={`fill-white ${className ?? ""}`}>
              <textPath
                ref={textPathRef}
                href={`#${pathId}`}
                startOffset={offset + "px"}
                xmlSpace="preserve"
              >
                {totalText}
              </textPath>
            </text>
          )}
        </svg>
      </div>
    );
  };
  
  export default CurvedLoop;
  