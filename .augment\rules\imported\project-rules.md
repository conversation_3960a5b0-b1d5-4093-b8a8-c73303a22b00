---
type: "always_apply"
---

# Project Rules for Remotion + React-bits

## Project Overview
This is a Remotion video project using React 19, TypeScript, Tailwind CSS v4, and react-bits component library for enhanced UI animations and effects.

## Technology Stack
- **Framework**: Remotion 4.0.340 for video generation
- **Frontend**: React 19 with TypeScript 5.8.2
- **Styling**: Tailwind CSS v4 with @remotion/tailwind-v4
- **Components**: react-bits for advanced animations and UI components
- **Linting**: ESLint with @remotion/eslint-config-flat
- **Formatting**: Prettier 3.6.0

## Code Style Guidelines

### TypeScript/React
- Use TypeScript for all new files (.tsx for React components, .ts for utilities)
- Prefer functional components with hooks over class components
- Use proper TypeScript types and interfaces, avoid `any`
- Export components as default exports for main components
- Use named exports for utilities and helper functions

### Component Structure
```typescript
// Preferred component structure
import React from 'react';
import { AbsoluteFill } from 'remotion';

interface ComponentProps {
  // Define props with proper types
}

export const ComponentName: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  return (
    <AbsoluteFill className="...">
      {/* Component content */}
    </AbsoluteFill>
  );
};

export default ComponentName;
```

### React-bits Integration
- Import react-bits components from the library when available
- Use react-bits for animations, text effects, backgrounds, and interactive components
- Prefer react-bits components over custom implementations for common UI patterns
- Categories available:
  - **TextAnimations**: For text effects and typography animations
  - **Animations**: For interactive animations and cursor effects
  - **Components**: For complete UI components with advanced interactions
  - **Backgrounds**: For immersive background effects

### Remotion Specific
- Use `AbsoluteFill` for full-screen layouts
- Use `useCurrentFrame()` and `useVideoConfig()` for frame-based animations
- Prefer `interpolate()` for smooth value transitions
- Use `Sequence` for timing different parts of the composition
- Keep compositions in separate files and register them in `src/Root.tsx`

### Styling with Tailwind CSS
- Use Tailwind CSS v4 classes for styling
- Prefer utility classes over custom CSS when possible
- Use responsive design patterns with Tailwind breakpoints
- Leverage Tailwind's animation utilities alongside react-bits components
- Custom CSS should be minimal and placed in `src/index.css`

### File Organization
```
src/
├── components/          # Reusable React components
├── compositions/        # Remotion composition files
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
├── assets/             # Static assets (images, fonts, etc.)
├── Composition.tsx     # Main composition
├── Root.tsx           # Remotion root with composition registry
└── index.ts           # Entry point
```

### Naming Conventions
- **Files**: PascalCase for components (`MyComponent.tsx`), camelCase for utilities (`myUtil.ts`)
- **Components**: PascalCase (`MyComponent`)
- **Variables/Functions**: camelCase (`myVariable`, `myFunction`)
- **Constants**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **Types/Interfaces**: PascalCase with descriptive names (`UserData`, `ComponentProps`)

### Performance Guidelines
- Use React.memo() for components that don't need frequent re-renders
- Optimize heavy computations with useMemo() and useCallback()
- Be mindful of frame rate - avoid expensive operations in render loops
- Use react-bits components which are optimized for performance
- Lazy load heavy assets when possible

### Error Handling
- Use proper error boundaries for React components
- Handle async operations with proper try-catch blocks
- Provide meaningful error messages for debugging
- Use TypeScript strict mode to catch errors at compile time

### Testing Considerations
- Write unit tests for utility functions
- Test component rendering with different props
- Test Remotion compositions at different frame positions
- Ensure react-bits components integrate properly with Remotion

### Dependencies Management
- Keep dependencies up to date, especially Remotion and react-bits
- Use exact versions for critical dependencies
- Prefer peer dependencies when possible to avoid version conflicts
- Document any specific version requirements

### Git Workflow
- Use conventional commit messages
- Keep commits focused and atomic
- Use meaningful branch names
- Include tests with new features

## Best Practices
1. **Performance**: Always consider the impact on render performance
2. **Accessibility**: Ensure animations don't cause motion sickness
3. **Responsiveness**: Test on different screen sizes
4. **Type Safety**: Use TypeScript features to prevent runtime errors
5. **Code Reuse**: Create reusable components and utilities
6. **Documentation**: Comment complex logic and animation sequences
7. **react-bits Integration**: Leverage the library's optimized components instead of reinventing

## 在使用过程中总结的建议
1. Remotion 中不能使用了相对单位（% / vw / vh / em / rem），统一用 绝对像素（px） 或 Remotion 提供的 useVideoConfig() 获取画布宽高：
const { width, height } = useVideoConfig();
<div style={{ width, height }} />
2. Remotion 中使用的组件必须是帧控制的，非帧控制的其他形式交互的组件需转换为帧控制组件才能使用