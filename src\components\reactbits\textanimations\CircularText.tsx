import React from "react";
import { useCurrentFrame } from "remotion";

interface CircularTextProps {
  text: string;
  spinDuration?: number; // 旋转一圈所需的帧数
  onHover?: "slowDown" | "speedUp" | "pause" | "goBonkers";
  className?: string;
  // 尺寸控制参数
  radius?: number; // 圆的半径，默认80px
  fontSize?: number; // 字体大小，默认24px
  containerSize?: number; // 容器大小，默认200px
  // Remotion Frame 控制参数
  startFrame?: number;
  endFrame?: number;
  hoverStartFrame?: number; // 指定帧开始 hover 效果
  hoverEndFrame?: number; // 指定帧结束 hover 效果
  autoRotate?: boolean; // 是否自动旋转
}

const CircularText: React.FC<CircularTextProps> = ({
  text,
  spinDuration = 120, // 默认120帧旋转一圈
  onHover = "speedUp",
  className = "",
  radius = 80, // 默认半径80px
  fontSize = 24, // 默认字体大小24px
  containerSize = 200, // 默认容器大小200px
  startFrame = 0,
  endFrame,
  hoverStartFrame,
  hoverEndFrame,
  autoRotate = true,
}) => {
  const frame = useCurrentFrame();
  const letters = Array.from(text);

  // 检查是否在显示范围内
  const isVisible = frame >= startFrame && (endFrame === undefined || frame <= endFrame);

  // 如果不在显示范围内，不渲染组件
  if (!isVisible) {
    return null;
  }

  // 计算当前是否在 hover 状态
  const isInHoverState = hoverStartFrame !== undefined && hoverEndFrame !== undefined &&
    frame >= hoverStartFrame && frame <= hoverEndFrame;

  // 计算旋转角度
  const getRotation = () => {
    if (!autoRotate) return 0;

    let effectiveSpinDuration = spinDuration;

    // 根据 hover 状态调整旋转速度
    if (isInHoverState) {
      switch (onHover) {
        case "slowDown":
          effectiveSpinDuration = spinDuration * 2;
          break;
        case "speedUp":
          effectiveSpinDuration = spinDuration / 4;
          break;
        case "pause":
          // 暂停时保持当前角度，不继续旋转
          const pauseFrame = hoverStartFrame || frame;
          const rotationProgress = (pauseFrame - startFrame) / spinDuration;
          return (rotationProgress * 360) % 360;
        case "goBonkers":
          effectiveSpinDuration = spinDuration / 20;
          break;
      }
    }

    // 基于帧数计算旋转角度
    const rotationProgress = (frame - startFrame) / effectiveSpinDuration;
    return (rotationProgress * 360) % 360;
  };

  // 计算缩放值
  const getScale = () => {
    if (isInHoverState && onHover === "goBonkers") {
      return 0.8;
    }
    return 1;
  };

  const rotation = getRotation();
  const scale = getScale();

  return (
    <div
      className={`circular-text ${className}`}
      style={{
        width: `${containerSize}px`,
        height: `${containerSize}px`,
        transform: `rotate(${rotation}deg) scale(${scale})`,
        WebkitTransform: `rotate(${rotation}deg) scale(${scale})`,
      }}
    >
      {letters.map((letter, i) => {
        const rotationDeg = (360 / letters.length) * i;
        const angle = (rotationDeg * Math.PI) / 180;
        const x = Math.sin(angle) * radius;
        const y = -Math.cos(angle) * radius;

        // 修正字符定位：先移动到圆心，再偏移到圆周，最后旋转字符本身
        const transform = `translate(-50%, -50%) translate(${x}px, ${y}px) rotate(${rotationDeg}deg)`;

        return (
          <span
            key={i}
            style={{
              position: "absolute",
              display: "inline-block",
              left: "50%",
              top: "50%",
              fontSize: `${fontSize}px`,
              transition: "all 0.5s cubic-bezier(0, 0, 0, 1)",
              transform,
              WebkitTransform: transform,
              transformOrigin: "center",
            }}
          >
            {letter}
          </span>
        );
      })}
    </div>
  );
};

export default CircularText;
