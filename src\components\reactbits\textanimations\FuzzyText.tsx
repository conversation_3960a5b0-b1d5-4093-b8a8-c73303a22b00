import React, { useEffect, useRef } from "react";
import { useCurrentFrame } from 'remotion';

interface FuzzyTextProps {
  children: React.ReactNode;
  fontSize?: number | string;
  fontWeight?: string | number;
  fontFamily?: string;
  color?: string;
  className?: string;
  baseIntensity?: number;
  hoverIntensity?: number;
  // Remotion Frame 控制参数
  startFrame?: number; // 动画开始帧
  endFrame?: number; // 动画结束帧
  duration?: number; // 动画持续时间（帧数）
  // 模糊效果控制参数
  intensityPattern?: number[] | number[][];
  // 强度变化数组格式: [startFrame, endFrame, chars, intensity] 或多个数组 如[[0, 30, 0, 1,1], [31, 60, 0,1, 2]] [0, 30, 0, 1,1]中0,30为帧范围，0,1为字符索引，1为强度
  clearPattern?: number[] | number[][];
  // 清晰效果数组格式: [startFrame, endFrame, chars] 或多个数组 [0, 30, 5 ,6]中0,30为帧范围，5,6为字符索引
}

const FuzzyText: React.FC<FuzzyTextProps> = ({
  children,
  fontSize = "clamp(2rem, 8vw, 8rem)",
  fontWeight = 900,
  fontFamily = "inherit",
  color = "#fff",
  className = "",
  baseIntensity = 0.18,
  hoverIntensity = 0.5,
  // Remotion Frame 参数
  startFrame = 0,
  endFrame,
  duration = 120, // 默认120帧
  // 模糊效果控制参数
  intensityPattern, // [startFrame, endFrame, chars, intensity]
  clearPattern, // [startFrame, endFrame, chars]
}) => {
  const frame = useCurrentFrame();
  const canvasRef = useRef<HTMLCanvasElement & { cleanupFuzzyText?: () => void }>(null);

  // 基于帧的随机数生成器（确保同一帧的随机值一致）
  const getFrameBasedRandom = (seed: number) => {
    // 使用帧数和种子创建伪随机数
    const x = Math.sin(frame * 12.9898 + seed * 78.233) * 43758.5453;
    return x - Math.floor(x);
  };

  // 计算实际的动画帧范围
  const actualStartFrame = startFrame;
  const actualEndFrame = endFrame || (startFrame + duration);

  // 检查是否在显示范围内
  const isVisible = frame >= actualStartFrame && frame <= actualEndFrame;

  // 基于帧和字符索引计算当前模糊强度
  const getCurrentIntensity = (charIndex?: number) => {
    // 处理清晰效果
    const processPattern = (pattern: number[] | number[][], isIntensity: boolean = false) => {
      // 检查是否是多个数组
      if (Array.isArray(pattern[0])) {
        // 多个数组格式
        const patterns = pattern as number[][];
        for (const singlePattern of patterns) {
          const result = processSinglePattern(singlePattern, isIntensity);
          if (result !== null) return result;
        }
      } else {
        // 单个数组格式
        const result = processSinglePattern(pattern as number[], isIntensity);
        if (result !== null) return result;
      }
      return null;
    };

    const processSinglePattern = (pattern: number[], isIntensity: boolean = false) => {
      if (pattern.length === 2) {
        // 格式: [startFrame, endFrame] - 应用到全部字符
        const [start, end] = pattern;
        if (frame >= start && frame <= end) {
          return isIntensity ? pattern[2] || baseIntensity : 0; // 清晰效果返回0
        }
      } else if (pattern.length === 3) {
        if (isIntensity) {
          // 格式: [startFrame, endFrame, intensity] - 应用到全部字符
          const [start, end, intensity] = pattern;
          if (frame >= start && frame <= end) {
            return intensity;
          }
        } else {
          // 格式: [startFrame, endFrame, charIndex] - 应用到单个字符
          const [start, end, charIdx] = pattern;
          if (frame >= start && frame <= end) {
            if (charIndex !== undefined && charIndex === charIdx) {
              return 0; // 指定字符完全清晰
            }
          }
        }
      } else if (pattern.length >= 4) {
        if (isIntensity) {
          // 格式: [startFrame, endFrame, ...chars, intensity] - 应用到指定字符
          const [start, end, ...rest] = pattern;
          const intensity = rest[rest.length - 1]; // 最后一个元素是强度
          const chars = rest.slice(0, -1); // 除了最后一个元素都是字符索引

          if (frame >= start && frame <= end) {
            if (charIndex !== undefined && chars.includes(charIndex)) {
              return intensity; // 指定字符使用特定强度
            }
          }
        } else {
          // 格式: [startFrame, endFrame, ...chars] - 应用到多个字符
          const [start, end, ...chars] = pattern;
          if (frame >= start && frame <= end) {
            if (charIndex !== undefined && chars.includes(charIndex)) {
              return 0; // 指定字符完全清晰
            }
          }
        }
      }
      return null;
    };

    // 检查清晰效果（优先级最高）
    if (clearPattern) {
      const clearResult = processPattern(clearPattern, false);
      if (clearResult !== null) return clearResult;
    }

    // 检查强度变化效果
    if (intensityPattern) {
      const intensityResult = processPattern(intensityPattern, true);
      if (intensityResult !== null) return intensityResult;
    }

    // 默认使用基础强度
    return baseIntensity;
  };

  useEffect(() => {
    let animationFrameId: number;
    let isCancelled = false;
    const canvas = canvasRef.current;
    if (!canvas) return;

    const init = async () => {
      if (document.fonts?.ready) {
        await document.fonts.ready;
      }
      if (isCancelled) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const computedFontFamily =
        fontFamily === "inherit"
          ? window.getComputedStyle(canvas).fontFamily || "sans-serif"
          : fontFamily;

      const fontSizeStr =
        typeof fontSize === "number" ? `${fontSize}px` : fontSize;
      let numericFontSize: number;
      if (typeof fontSize === "number") {
        numericFontSize = fontSize;
      } else {
        const temp = document.createElement("span");
        temp.style.fontSize = fontSize;
        document.body.appendChild(temp);
        const computedSize = window.getComputedStyle(temp).fontSize;
        numericFontSize = parseFloat(computedSize);
        document.body.removeChild(temp);
      }

      const text = React.Children.toArray(children).join("");
      const characters = Array.from(text);

      // 创建字符位置映射
      const charPositions: Array<{ char: string, x: number, width: number }> = [];

      const tempCtx = document.createElement("canvas").getContext("2d");
      if (!tempCtx) return;
      tempCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;

      let currentX = 0;
      characters.forEach((char, index) => {
        const charWidth = tempCtx.measureText(char).width;
        charPositions.push({
          char,
          x: currentX,
          width: charWidth
        });
        currentX += charWidth;
      });

      const totalTextWidth = currentX;
      const metrics = tempCtx.measureText(text);
      const actualLeft = metrics.actualBoundingBoxLeft ?? 0;
      const actualAscent = metrics.actualBoundingBoxAscent ?? numericFontSize;
      const actualDescent = metrics.actualBoundingBoxDescent ?? numericFontSize * 0.2;
      const tightHeight = Math.ceil(actualAscent + actualDescent);

      const extraWidthBuffer = 10;
      const offscreenWidth = Math.ceil(totalTextWidth) + extraWidthBuffer;

      // 为每个字符创建单独的离屏画布
      const charCanvases: HTMLCanvasElement[] = [];
      characters.forEach((char, index) => {
        const charCanvas = document.createElement("canvas");
        const charCtx = charCanvas.getContext("2d");
        if (!charCtx) return;

        const charWidth = Math.ceil(charPositions[index].width) + 4;
        charCanvas.width = charWidth;
        charCanvas.height = tightHeight;

        charCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;
        charCtx.textBaseline = "alphabetic";
        charCtx.fillStyle = color;
        charCtx.fillText(char, 2, actualAscent);

        charCanvases.push(charCanvas);
      });

      const horizontalMargin = 50;
      const verticalMargin = 0;
      canvas.width = offscreenWidth + horizontalMargin * 2;
      canvas.height = tightHeight + verticalMargin * 2;
      ctx.translate(horizontalMargin, verticalMargin);

      const fuzzRange = 30;

      // 获取字符级别的强度
      const getCharacterIntensity = (charIndex: number) => {
        // 使用帧控制的强度设置（包含字符索引检查）
        return getCurrentIntensity(charIndex);
      };

      const run = () => {
        if (isCancelled || !isVisible) return;
        ctx.clearRect(
          -fuzzRange,
          -fuzzRange,
          offscreenWidth + 2 * fuzzRange,
          tightHeight + 2 * fuzzRange
        );

        // 渲染每个字符
        let currentX = extraWidthBuffer / 2;
        charCanvases.forEach((charCanvas, charIndex) => {
          const intensity = getCharacterIntensity(charIndex);
          const charWidth = charPositions[charIndex].width;

          if (intensity === 0) {
            // 直接渲染清晰字符
            ctx.drawImage(charCanvas, currentX, 0);
          } else {
            // 渲染模糊字符
            for (let j = 0; j < tightHeight; j++) {
              // 使用基于帧的随机数，确保同一帧的抖动一致
              const seed = charIndex * 1000 + j; // 为每个字符和行创建唯一种子
              const randomValue = getFrameBasedRandom(seed);
              const dx = Math.floor(intensity * (randomValue - 0.5) * fuzzRange);
              ctx.drawImage(
                charCanvas,
                0,
                j,
                charCanvas.width,
                1,
                currentX + dx,
                j,
                charCanvas.width,
                1
              );
            }
          }

          currentX += charWidth;
        });

        animationFrameId = window.requestAnimationFrame(run);
      };

      run();

      const cleanup = () => {
        window.cancelAnimationFrame(animationFrameId);
      };

      canvas.cleanupFuzzyText = cleanup;
    };

    init();

    return () => {
      isCancelled = true;
      window.cancelAnimationFrame(animationFrameId);
      if (canvas && canvas.cleanupFuzzyText) {
        canvas.cleanupFuzzyText();
      }
    };
  }, [
    children,
    fontSize,
    fontWeight,
    fontFamily,
    color,
    className,
    baseIntensity,
    hoverIntensity,
    frame,
    isVisible,
    intensityPattern,
    clearPattern,
  ]);

  // 如果不在显示时间范围内，不渲染
  if (!isVisible) {
    return null;
  }

  return <canvas ref={canvasRef} className={className} />;
};

export default FuzzyText;
