import React from 'react';
import { AbsoluteFill } from 'remotion';
import GradientText from '../reactbits/textanimations/GradientText';

const GradientTextMultiPatternExample: React.FC = () => {
  return (
    <AbsoluteFill className="bg-black flex flex-col items-center justify-center gap-16">
      {/* 单个渐变模式 */}
      <GradientText
        startFrame={0}
        endFrame={120}
        colors={["#ff6b6b", "#4ecdc4", "#45b7d1"]}
        gradientPattern={[30, 90, 0, 100]} // 30-90帧从0%到100%
        className="text-4xl font-bold"
      >
        SINGLE PATTERN
      </GradientText>

      {/* 多个渐变模式 */}
      <GradientText
        startFrame={120}
        endFrame={240}
        colors={["#ffd93d", "#6bcf7f", "#4d96ff"]}
        gradientPattern={[
          [150, 180, 0, 50],   // 150-180帧从0%到50%
          [190, 220, 50, 100]  // 190-220帧从50%到100%
        ]}
        className="text-3xl font-bold"
      >
        MULTI PATTERN
      </GradientText>

      {/* 复杂的多段动画 */}
      <GradientText
        startFrame={240}
        endFrame={360}
        colors={["#ff9a9e", "#fecfef", "#fecfef"]}
        gradientPattern={[
          [270, 300, 0, 100],   // 第一段：快速移动到右边
          [310, 340, 100, 0],   // 第二段：快速移动到左边
          [350, 360, 0, 50]     // 第三段：移动到中间
        ]}
        showBorder={true}
        className="text-3xl font-bold px-6 py-3"
      >
        COMPLEX ANIMATION
      </GradientText>

      {/* 重叠的渐变效果 */}
      <GradientText
        startFrame={360}
        endFrame={480}
        colors={["#a8edea", "#fed6e3", "#d299c2"]}
        gradientPattern={[
          [380, 420, 0, 80],    // 第一段动画
          [400, 440, 20, 100],  // 第二段动画（与第一段重叠）
          [430, 470, 100, 30]   // 第三段动画
        ]}
        className="text-4xl font-bold"
      >
        OVERLAPPING
      </GradientText>

      {/* 混合模式：部分使用模式，部分使用默认循环 */}
      <GradientText
        startFrame={480}
        endFrame={600}
        colors={["#667eea", "#764ba2", "#f093fb"]}
        gradientPattern={[
          [500, 530, 0, 100],   // 只在500-530帧使用自定义模式
          [560, 590, 100, 0]    // 在560-590帧使用另一个模式
        ]}
        cycleFrames={40} // 其他时间使用默认循环
        className="text-3xl font-bold"
      >
        MIXED MODE
      </GradientText>
    </AbsoluteFill>
  );
};

export default GradientTextMultiPatternExample;
