import { useEffect, useRef, useState } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';

interface TextPressureProps {
    text?: string;
    fontFamily?: string;
    fontUrl?: string;
    width?: boolean;
    weight?: boolean;
    italic?: boolean;
    alpha?: boolean;
    flex?: boolean;
    stroke?: boolean;
    scale?: boolean;
    textColor?: string;
    strokeColor?: string;
    strokeWidth?: number;
    className?: string;
    minFontSize?: number;
    // Remotion Frame 控制参数
    startFrame?: number; // 动画开始帧
    endFrame?: number; // 动画结束帧
    duration?: number; // 动画持续时间（帧数）
    // 模拟鼠标移动参数
    movementPattern?: number[]; // 移动数组格式: [startPos, endPos, startFrame, endFrame, speed?]
    // startPos/endPos: 0-100，表示从左到右的位置百分比
    // startFrame/endFrame: 动画的帧范围
    // speed: 移动速度（可选，默认为1）
    centerY?: number; // 中心点Y坐标（相对于容器高度）
}

const TextPressure: React.FC<TextPressureProps> = ({
    text = 'Compressa',
    fontFamily = 'Compressa VF',
    fontUrl = 'https://res.cloudinary.com/dr6lvwubh/raw/upload/v1529908256/CompressaPRO-GX.woff2',
    width = true,
    weight = true,
    italic = true,
    alpha = false,
    flex = true,
    stroke = false,
    scale = false,
    textColor = '#FFFFFF',
    strokeColor = '#FF0000',
    strokeWidth = 2,
    className = '',
    minFontSize = 24,
    // Remotion Frame 参数
    startFrame = 0,
    endFrame,
    duration = 120, // 默认120帧
    // useFrameBasedAnimation 始终为 true
    // 模拟鼠标移动参数
    movementPattern = [0, 100, 0, 120, 1], // 默认：从0到100位置，0到120帧，速度1
    centerY = 0.5, // 相对于容器高度的比例
}) => {
    const frame = useCurrentFrame();

    const containerRef = useRef<HTMLDivElement | null>(null);
    const titleRef = useRef<HTMLHeadingElement | null>(null);
    const spansRef = useRef<(HTMLSpanElement | null)[]>([]);

    const mouseRef = useRef({ x: 0, y: 0 });
    const cursorRef = useRef({ x: 0, y: 0 });

    const [fontSize, setFontSize] = useState(minFontSize);
    const [scaleY, setScaleY] = useState(1);
    const [lineHeight, setLineHeight] = useState(1);

    const chars = text.split('');

    // 计算实际的动画帧范围
    const actualStartFrame = startFrame;
    const actualEndFrame = endFrame || (startFrame + duration);

    // 检查是否在显示范围内
    const isVisible = frame >= actualStartFrame && frame <= actualEndFrame;

    const dist = (a: { x: number; y: number }, b: { x: number; y: number }) => {
        const dx = b.x - a.x;
        const dy = b.y - a.y;
        return Math.sqrt(dx * dx + dy * dy);
    };

    // 基于帧计算模拟鼠标位置
    const getSimulatedMousePosition = () => {
        if (!containerRef.current || !movementPattern || movementPattern.length < 4) {
            // 如果没有移动模式，返回容器中心
            const containerRect = containerRef.current?.getBoundingClientRect();
            if (containerRect) {
                return {
                    x: containerRect.left + containerRect.width * 0.5,
                    y: containerRect.top + containerRect.height * centerY
                };
            }
            return { x: 0, y: 0 };
        }

        const containerRect = containerRef.current.getBoundingClientRect();

        // 解析移动数组 [startPos, endPos, startFrame, endFrame, speed?]
        const [startPos, endPos, moveStartFrame, moveEndFrame, speed = 1] = movementPattern;

        // 默认位置是数组起始位置
        let currentPos = startPos;

        // 检查当前帧是否在移动范围内
        if (frame >= moveStartFrame && frame <= moveEndFrame) {
            // 在移动范围内，计算当前位置进度
            const frameProgress = (frame - moveStartFrame) / (moveEndFrame - moveStartFrame);
            const adjustedProgress = Math.min(1, Math.max(0, frameProgress * speed));

            // 使用 interpolate 计算当前位置
            currentPos = interpolate(adjustedProgress, [0, 1], [startPos, endPos], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
            });
        } else if (frame > moveEndFrame) {
            // 超过移动范围，使用结束位置
            currentPos = endPos;
        }
        // 如果 frame < moveStartFrame，保持默认的 startPos

        // 转换为像素坐标
        const x = containerRect.left + (containerRect.width * currentPos / 100);
        const y = containerRect.top + containerRect.height * centerY;

        return { x, y };
    };

    useEffect(() => {
        // 始终使用帧控制，初始化鼠标位置为数组起始位置
        if (containerRef.current) {
            const { left, top, width, height } = containerRef.current.getBoundingClientRect();

            // 如果有移动模式，使用起始位置，否则使用中心
            let initialX = left + width / 2;
            if (movementPattern && movementPattern.length >= 2) {
                const startPos = movementPattern[0];
                initialX = left + (width * startPos / 100);
            }

            const initialY = top + height * centerY;

            mouseRef.current.x = initialX;
            mouseRef.current.y = initialY;
            cursorRef.current.x = initialX;
            cursorRef.current.y = initialY;
        }
    }, [movementPattern, centerY]);

    const setSize = () => {
        if (!containerRef.current || !titleRef.current) return;

        const { width: containerW, height: containerH } = containerRef.current.getBoundingClientRect();

        let newFontSize = containerW / (chars.length / 2);
        newFontSize = Math.max(newFontSize, minFontSize);

        setFontSize(newFontSize);
        setScaleY(1);
        setLineHeight(1);

        requestAnimationFrame(() => {
            if (!titleRef.current) return;
            const textRect = titleRef.current.getBoundingClientRect();

            if (scale && textRect.height > 0) {
                const yRatio = containerH / textRect.height;
                setScaleY(yRatio);
                setLineHeight(yRatio);
            }
        });
    };

    useEffect(() => {
        setSize();
        window.addEventListener('resize', setSize);
        return () => window.removeEventListener('resize', setSize);
    }, [scale, text]);

    useEffect(() => {
        if (!isVisible) return;

        const animate = () => {
            // 始终使用帧控制，更新鼠标位置为模拟位置
            const simulatedPos = getSimulatedMousePosition();
            cursorRef.current.x = simulatedPos.x;
            cursorRef.current.y = simulatedPos.y;

            mouseRef.current.x += (cursorRef.current.x - mouseRef.current.x) / 15;
            mouseRef.current.y += (cursorRef.current.y - mouseRef.current.y) / 15;

            if (titleRef.current) {
                const titleRect = titleRef.current.getBoundingClientRect();
                const maxDist = titleRect.width / 2;

                spansRef.current.forEach((span) => {
                    if (!span) return;

                    const rect = span.getBoundingClientRect();
                    const charCenter = {
                        x: rect.x + rect.width / 2,
                        y: rect.y + rect.height / 2,
                    };

                    const d = dist(mouseRef.current, charCenter);

                    const getAttr = (distance: number, minVal: number, maxVal: number) => {
                        const val = maxVal - Math.abs((maxVal * distance) / maxDist);
                        return Math.max(minVal, val + minVal);
                    };

                    const wdth = width ? Math.floor(getAttr(d, 5, 200)) : 100;
                    const wght = weight ? Math.floor(getAttr(d, 100, 900)) : 400;
                    const italVal = italic ? getAttr(d, 0, 1).toFixed(2) : '0';
                    const alphaVal = alpha ? getAttr(d, 0, 1).toFixed(2) : '1';

                    span.style.opacity = alphaVal;
                    span.style.fontVariationSettings = `'wght' ${wght}, 'wdth' ${wdth}, 'ital' ${italVal}`;
                });
            }
        };

        animate();
    }, [width, weight, italic, alpha, chars.length, frame, isVisible]);

    // 如果不在显示时间范围内，不渲染
    if (!isVisible) {
        return null;
    }

    return (
        <div
            ref={containerRef}
            className="relative w-full h-full overflow-hidden bg-transparent"
        >
            <style>{`
        @font-face {
          font-family: '${fontFamily}';
          src: url('${fontUrl}');
          font-style: normal;
        }
        .stroke span {
          position: relative;
          color: ${textColor};
        }
        .stroke span::after {
          content: attr(data-char);
          position: absolute;
          left: 0;
          top: 0;
          color: transparent;
          z-index: -1;
          -webkit-text-stroke-width: ${strokeWidth}px;
          -webkit-text-stroke-color: ${strokeColor};
        }
      `}</style>

            <h1
                ref={titleRef}
                className={`text-pressure-title ${className} ${flex ? 'flex justify-between' : ''
                    } ${stroke ? 'stroke' : ''} uppercase text-center`}
                style={{
                    fontFamily,
                    fontSize: fontSize,
                    lineHeight,
                    transform: `scale(1, ${scaleY})`,
                    transformOrigin: 'center top',
                    margin: 0,
                    fontWeight: 100,
                    color: stroke ? undefined : textColor,
                }}
            >
                {chars.map((char, i) => (
                    <span
                        key={i}
                        ref={(el) => {
                            if (el) {
                                spansRef.current[i] = el;
                            }
                        }}
                        data-char={char}
                        className="inline-block"
                    >
                        {char}
                    </span>
                ))}
            </h1>
        </div>
    );
};

export default TextPressure;
